# Global Navigation System v3.0 - Usage Guide

## 🌟 Overview

The Global Navigation System v3.0 provides a unified way to handle all navigation in the application with automatic authentication checks. **All existing navigation logic has been removed and replaced with this system.**

## 🚀 Quick Start

### For HTML Templates

Use data attributes on buttons and links:

```html
<!-- Login buttons (will return to current page after login) -->
<button data-login-link="true">Sign In</button>
<a href="/login/" data-login-link="true">Login</a>

<!-- Protected pages (requires authentication) -->
<button data-nav-url="/audio_upload/" data-auth-required="true">Start Screening</button>
<a href="/profile/" data-auth-required="true">My Profile</a>

<!-- Public pages (no authentication required) -->
<button data-nav-url="/about/">About Us</button>
<a href="/contact/">Contact</a>
```

### For JavaScript

Use global functions:

```javascript
// Navigate with authentication check
navigateTo('/audio_upload/', true);  // Requires login
navigateTo('/about/', false);        // No login required

// Go to login (will return to current page)
goToLogin();

// Handle successful login (call this after login)
handleLoginSuccess();
```

## 🔧 How It Works

1. **Automatic Detection**: The system automatically detects clicks on buttons and links
2. **Authentication Check**: For protected paths, it checks if the user is logged in
3. **Smart Redirects**: If login is required, it saves the intended destination and redirects to login
4. **Return Navigation**: After successful login, it automatically redirects to the saved destination

## 🛡️ Protected Paths

These paths automatically require authentication:
- `/profile/`
- `/settings/`
- `/audio_upload/`
- `/audio-history/`
- `/dashboard/`
- `/notifications/`
- `/user/`
- `/audio-detail/`

## 📝 Data Attributes

| Attribute | Purpose | Example |
|-----------|---------|---------|
| `data-login-link="true"` | Login button that returns to current page | `<button data-login-link="true">Login</button>` |
| `data-nav-url="/path/"` | Navigate to specific URL | `<button data-nav-url="/about/">About</button>` |
| `data-auth-required="true"` | Force authentication check | `<a href="/profile/" data-auth-required="true">Profile</a>` |

## 🔄 Migration from Old System

**No changes needed!** The new system:
- ✅ Automatically handles existing `href` attributes for protected paths
- ✅ Provides backward compatibility with old function calls
- ✅ Works with existing button click handlers
- ✅ Maintains all existing functionality

## 🧪 Testing

Visit `/test-navigation/` to test the navigation system and verify it's working correctly.

## 🐛 Troubleshooting

### Navigation not working?
1. Check browser console for errors
2. Verify `global_navigation.js` is loaded
3. Ensure data attributes are correct

### Not redirecting after login?
1. Verify `handleLoginSuccess()` is called after successful login
2. Check browser console for navigation logs
3. Clear browser storage and try again

### Infinite redirects?
1. Check if the return URL is valid
2. Clear session storage: `sessionStorage.clear()`
3. Verify token is valid

## 📊 Console Logging

The system provides detailed console logging:
- 🌐 System initialization
- 🧭 Navigation requests
- 🔐 Authentication checks
- 💾 Return URL management
- ✅ Successful operations
- ❌ Error conditions

Enable browser console to see detailed operation logs.

---

**Status**: ✅ Fully Implemented and Ready to Use
**Version**: 3.0
**Last Updated**: 2025-01-08
