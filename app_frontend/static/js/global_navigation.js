/**
 * Global Navigation System v5.0
 * Complete navigation system with automatic authentication checks
 * 
 * Features:
 * - Intercepts ALL navigation attempts (buttons, links, JavaScript)
 * - Automatic authentication checks for protected routes
 * - Login flow with return URL management
 * - Universal coverage across all pages
 */

class GlobalNavigationSystem {
    constructor() {
        // Get configuration from Django settings
        this.API_BASE_URL = window.API_BASE_URL || 'http://**************:8001';
        this.LOCAL_BASE_URL = window.LOCAL_BASE_URL || 'http://**************:8000';
        
        console.log('🌐 Global Navigation System v5.0 initializing...');
        console.log('📡 API Base URL:', this.API_BASE_URL);
        console.log('🏠 Local Base URL:', this.LOCAL_BASE_URL);
        
        this.init();
    }

    /**
     * Initialize the navigation system
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupGlobalHandlers());
        } else {
            this.setupGlobalHandlers();
        }
    }

    /**
     * Set up global navigation handlers
     */
    setupGlobalHandlers() {
        console.log('🔧 Setting up global navigation handlers...');
        
        // Intercept ALL clicks on the document
        document.addEventListener('click', (event) => this.handleAllClicks(event), true);
        
        // Intercept form submissions that might navigate
        document.addEventListener('submit', (event) => this.handleFormSubmissions(event), true);
        
        // Override window.location assignments
        this.overrideWindowLocation();
        
        console.log('✅ Global navigation handlers active');
    }

    /**
     * Handle all click events globally
     */
    handleAllClicks(event) {
        const element = event.target.closest('a, button, [onclick], [data-nav-url]');
        if (!element) return;

        // Get potential navigation URL
        const navUrl = this.extractNavigationUrl(element);
        if (!navUrl) return;

        // Skip special URLs
        if (this.isSpecialUrl(navUrl)) return;

        // Determine if this is a login action
        const isLoginAction = this.isLoginAction(element, navUrl);
        
        if (isLoginAction) {
            event.preventDefault();
            event.stopPropagation();
            this.handleLoginAction();
            return;
        }

        // Check if navigation requires authentication
        const requiresAuth = this.requiresAuthentication(navUrl);
        
        if (requiresAuth) {
            event.preventDefault();
            event.stopPropagation();
            this.handleProtectedNavigation(navUrl);
            return;
        }

        // For public URLs, allow normal navigation
        console.log('🌐 Public navigation allowed:', navUrl);
    }

    /**
     * Extract navigation URL from element
     */
    extractNavigationUrl(element) {
        // Check various sources for navigation URL
        let url = element.getAttribute('data-nav-url') ||
                 element.getAttribute('href') ||
                 element.getAttribute('action');

        // Check onclick attribute for location assignments
        if (!url && element.getAttribute('onclick')) {
            const onclickContent = element.getAttribute('onclick');
            const locationMatch = onclickContent.match(/(?:window\.)?location\.href\s*=\s*['"`]([^'"`]+)['"`]/);
            if (locationMatch) {
                url = locationMatch[1];
            }
        }

        return url;
    }

    /**
     * Check if URL is special (should not be intercepted)
     */
    isSpecialUrl(url) {
        return !url || 
               url === '#' || 
               url.startsWith('javascript:') || 
               url.startsWith('mailto:') || 
               url.startsWith('tel:') ||
               url.startsWith('data:') ||
               url.startsWith('blob:');
    }

    /**
     * Check if this is a login action
     */
    isLoginAction(element, url) {
        return element.id === 'global-signin-btn' ||
               element.classList.contains('signin') ||
               element.classList.contains('login-btn') ||
               element.getAttribute('data-login') === 'true' ||
               (url && url.includes('/login'));
    }

    /**
     * Check if URL requires authentication
     */
    requiresAuthentication(url) {
        const protectedPaths = [
            '/user/',
            '/profile/',
            '/settings/',
            '/audio_upload/',
            '/audio-history/',
            '/dashboard/',
            '/notifications/',
            '/audio-detail/',
            '/message/'
        ];
        
        return protectedPaths.some(path => url.includes(path));
    }

    /**
     * Handle login action
     */
    handleLoginAction() {
        console.log('🔑 Login action detected');
        
        // Save current page as return URL
        const currentUrl = window.location.href;
        if (!currentUrl.includes('/login') && !currentUrl.includes('/register')) {
            sessionStorage.setItem('return_after_login', currentUrl);
            console.log('💾 Saved return URL:', currentUrl);
        }
        
        // Navigate to login
        window.location.href = '/login/';
    }

    /**
     * Handle protected navigation
     */
    handleProtectedNavigation(url) {
        console.log('🔒 Protected navigation to:', url);
        
        if (this.isAuthenticated()) {
            console.log('✅ User authenticated, proceeding to:', url);
            window.location.href = url;
        } else {
            console.log('❌ Authentication required, redirecting to login');
            // Save target URL for after login
            sessionStorage.setItem('return_after_login', url);
            window.location.href = '/login/';
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token) return false;
        
        try {
            const payload = JSON.parse(atob(token.split('.')[1]));
            const now = Date.now() / 1000;
            return payload.exp > now;
        } catch (error) {
            console.warn('🔍 Token validation failed:', error);
            return false;
        }
    }

    /**
     * Handle form submissions
     */
    handleFormSubmissions(event) {
        const form = event.target;
        const action = form.getAttribute('action');
        
        if (action && this.requiresAuthentication(action)) {
            if (!this.isAuthenticated()) {
                event.preventDefault();
                console.log('🔒 Form submission requires authentication');
                sessionStorage.setItem('return_after_login', window.location.href);
                window.location.href = '/login/';
            }
        }
    }

    /**
     * Override window.location to intercept JavaScript navigation
     */
    overrideWindowLocation() {
        const originalLocation = window.location;
        let isNavigating = false;

        // Override href setter
        Object.defineProperty(window.location, 'href', {
            set: (url) => {
                if (isNavigating) return;
                
                if (this.requiresAuthentication(url)) {
                    if (!this.isAuthenticated()) {
                        console.log('🔒 JavaScript navigation requires authentication:', url);
                        sessionStorage.setItem('return_after_login', url);
                        isNavigating = true;
                        originalLocation.href = '/login/';
                        return;
                    }
                }
                
                console.log('🌐 JavaScript navigation allowed:', url);
                isNavigating = true;
                originalLocation.href = url;
            },
            get: () => originalLocation.href
        });
    }

    /**
     * Handle successful login (call this after login success)
     */
    handleLoginSuccess() {
        console.log('🎉 Login successful, handling redirect...');
        
        const returnUrl = sessionStorage.getItem('return_after_login');
        
        if (returnUrl && !returnUrl.includes('/login') && !returnUrl.includes('/register')) {
            console.log('📍 Redirecting to saved URL:', returnUrl);
            sessionStorage.removeItem('return_after_login');
            window.location.replace(returnUrl);
        } else {
            console.log('🏠 No return URL, redirecting to home');
            window.location.replace('/');
        }
    }

    /**
     * Public method to navigate with auth check
     */
    navigateTo(url, forceAuthCheck = false) {
        if (forceAuthCheck || this.requiresAuthentication(url)) {
            this.handleProtectedNavigation(url);
        } else {
            window.location.href = url;
        }
    }

    /**
     * Public method to go to login
     */
    goToLogin() {
        this.handleLoginAction();
    }
}

// Initialize the global navigation system
const globalNav = new GlobalNavigationSystem();

// Export global functions for easy access
window.globalNav = globalNav;
window.navigateTo = (url, requiresAuth = false) => globalNav.navigateTo(url, requiresAuth);
window.goToLogin = () => globalNav.goToLogin();
window.handleLoginSuccess = () => globalNav.handleLoginSuccess();

console.log('🌐 Global Navigation System v5.0 loaded successfully');
