/**
 * Global Navigation System v6.0 - Enhanced Universal Authentication Manager
 * Handles smart navigation with login flow and universal authentication checks
 * Ensures back button skips login page and returns to original page
 * Provides global authentication checks for all buttons and navigation
 */

class GlobalNavigationSystem {
    constructor() {
        // Get configuration from Django settings
        this.API_BASE_URL = window.API_BASE_URL || 'http://**************:8001';
        this.LOCAL_BASE_URL = window.LOCAL_BASE_URL || 'http://**************:8000';

        console.log('🌐 Global Navigation System v6.0 initializing...');
        console.log('📡 API Base URL:', this.API_BASE_URL);
        console.log('🏠 Local Base URL:', this.LOCAL_BASE_URL);

        this.init();
    }

    init() {
        // Store original page when navigation starts
        this.storeOriginalPage();

        // Handle browser back/forward buttons
        window.addEventListener('popstate', (event) => {
            this.handlePopState(event);
        });

        // Setup global handlers when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupGlobalHandlers());
        } else {
            this.setupGlobalHandlers();
        }
    }

    /**
     * Setup global event handlers for authentication and navigation
     */
    setupGlobalHandlers() {
        console.log('🔧 Setting up global navigation handlers...');

        // Setup click handlers for all links and buttons
        this.setupLinkHandlers();
        this.setupButtonHandlers();
        this.setupLoginButtonHandlers();

        // Override window.location for JavaScript navigation
        this.overrideWindowLocation();

        console.log('✅ Global navigation handlers setup complete');
    }

    /**
     * Setup click handlers for all links
     */
    setupLinkHandlers() {
        // Handle all anchor tags with href attributes
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && !link.hasAttribute('data-nav-handled')) {
                const href = link.getAttribute('href');

                // Skip external links, anchors, and special links
                if (this.shouldSkipLink(href)) return;

                // Check if this link requires authentication
                if (this.requiresAuthentication(href)) {
                    e.preventDefault();
                    console.log('🔗 Link requires authentication:', href);
                    this.handleAuthenticatedNavigation(href);
                }
            }
        });
    }

    /**
     * Setup click handlers for all buttons
     */
    setupButtonHandlers() {
        // Handle buttons with data-nav-url attributes
        document.addEventListener('click', (e) => {
            const button = e.target.closest('button[data-nav-url], .btn[data-nav-url]');
            if (button) {
                e.preventDefault();
                const targetUrl = button.getAttribute('data-nav-url');
                const requiresAuth = button.hasAttribute('data-requires-auth');

                console.log('🔘 Button navigation:', { targetUrl, requiresAuth });

                if (requiresAuth && !this.isAuthenticated()) {
                    this.handleAuthenticatedNavigation(targetUrl);
                } else {
                    window.location.href = targetUrl;
                }
            }
        });
    }

    /**
     * Setup universal login button handlers
     */
    setupLoginButtonHandlers() {
        // Handle all login/signin buttons with event delegation
        document.addEventListener('click', (e) => {
            console.log('🔍 Click detected on:', e.target);

            // Check if the clicked element or its parent is a login button
            const element = e.target.closest('[data-login-link="true"], #global-signin-btn, .signin, a[href="/login/"], button[onclick*="login"], a[onclick*="login"]');

            if (element) {
                console.log('🔑 Login button detected:', element);

                // Check various login button conditions
                const isLoginButton =
                    element.hasAttribute('data-login-link') ||
                    element.getAttribute('data-login-link') === 'true' ||
                    element.id === 'global-signin-btn' ||
                    element.classList.contains('signin') ||
                    element.getAttribute('href') === '/login/' ||
                    (element.getAttribute('onclick') && element.getAttribute('onclick').includes('login'));

                if (isLoginButton) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🔑 Login action triggered');
                    this.handleLoginAction();
                    return false;
                }
            }
        }, true); // Use capture phase to ensure we catch the event
    }

    /**
     * Store the current page as the original page before navigation
     */
    storeOriginalPage() {
        if (!sessionStorage.getItem('originalPage')) {
            sessionStorage.setItem('originalPage', window.location.href);
        }
    }

    /**
     * Check if user is authenticated with enhanced token validation
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        if (!token || token === 'null' || token === '') {
            return false;
        }

        try {
            // Decode JWT token to check expiration
            const payload = JSON.parse(atob(token.split('.')[1]));
            const currentTime = Date.now() / 1000;

            if (payload.exp < currentTime) {
                console.log('❌ Token expired');
                this.clearAuthData();
                return false;
            }

            return true;
        } catch (error) {
            console.error('❌ Error checking token:', error);
            this.clearAuthData();
            return false;
        }
    }

    /**
     * Clear authentication data
     */
    clearAuthData() {
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
    }

    /**
     * Check if a link should be skipped by the navigation system
     */
    shouldSkipLink(href) {
        return !href ||
               href.startsWith('#') ||
               href.startsWith('mailto:') ||
               href.startsWith('tel:') ||
               href.startsWith('http://') ||
               href.startsWith('https://') ||
               href.includes('javascript:');
    }

    /**
     * Check if URL requires authentication
     */
    requiresAuthentication(url) {
        const protectedPaths = [
            '/user/',
            '/profile/',
            '/settings/',
            '/audio_upload/',
            '/audio-history/',
            '/dashboard/',
            '/notifications/',
            '/audio-detail/',
            '/message/'
        ];

        return protectedPaths.some(path => url.includes(path));
    }

    /**
     * Handle navigation that requires authentication
     */
    handleAuthenticatedNavigation(targetUrl) {
        console.log('🔒 Authentication required for:', targetUrl);

        // Save current page as return URL
        const currentUrl = window.location.href;
        sessionStorage.setItem('redirectAfterLogin', targetUrl);
        sessionStorage.setItem('originalPageBeforeLogin', currentUrl);

        console.log('💾 Saved navigation context:', {
            redirectAfterLogin: targetUrl,
            originalPageBeforeLogin: currentUrl
        });

        // Navigate to login
        window.location.href = '/login/';
    }

    /**
     * Handle login action (for login buttons)
     */
    handleLoginAction() {
        console.log('🔑 Login action detected - starting redirect process');

        // Save current page as return URL
        const currentUrl = window.location.href;
        console.log('🌐 Current URL:', currentUrl);

        if (!currentUrl.includes('/login') && !currentUrl.includes('/register')) {
            sessionStorage.setItem('redirectAfterLogin', currentUrl);
            sessionStorage.setItem('originalPageBeforeLogin', currentUrl);
            console.log('💾 Saved return URL to sessionStorage:', currentUrl);
        } else {
            console.log('⚠️ Already on login/register page, not saving return URL');
        }

        // Navigate to login
        console.log('🔄 Redirecting to login page...');
        window.location.href = '/login/';
    }

    /**
     * Navigate to a page with login check
     * @param {string} targetUrl - The URL to navigate to
     * @param {boolean} requiresAuth - Whether the page requires authentication
     * @param {string} buttonId - ID of the button that triggered navigation (optional)
     */
    navigateWithAuthCheck(targetUrl, requiresAuth = false, buttonId = null) {
        console.log('🔍 Navigation requested:', { targetUrl, requiresAuth, buttonId });

        if (requiresAuth && !this.isAuthenticated()) {
            this.handleAuthenticatedNavigation(targetUrl);
        } else {
            console.log('✅ Navigation allowed, going to:', targetUrl);

            // Clear any stored redirect URLs since we're navigating directly
            sessionStorage.removeItem('redirectAfterLogin');
            sessionStorage.removeItem('originalPageBeforeLogin');

            // Navigate to target page
            window.location.href = targetUrl;
        }
    }

    /**
     * Handle successful login - redirect to intended page and manipulate history
     */
    handleLoginSuccess() {
        const redirectUrl = sessionStorage.getItem('redirectAfterLogin');
        const originalPage = sessionStorage.getItem('originalPageBeforeLogin');

        console.log('✅ Login successful, handling redirect:', { redirectUrl, originalPage });

        if (redirectUrl && originalPage) {
            // Check if we're redirecting to a different page than where user came from
            const redirectPath = new URL(redirectUrl, window.location.origin).pathname;
            const originalPath = new URL(originalPage, window.location.origin).pathname;

            if (redirectPath !== originalPath) {
                console.log('🔄 Different page redirect detected, using special history handling');

                // For different page redirects (like Contact Us case),
                // we need to set up the history so back button goes to original page

                // Clear the redirect URL
                sessionStorage.removeItem('redirectAfterLogin');

                // Store a flag to indicate we need special history handling
                sessionStorage.setItem('needsHistoryFix', 'true');

                // Navigate to redirect URL
                window.location.replace(redirectUrl);
            } else {
                console.log('🔄 Same page redirect, using normal handling');

                // Clear both values for same-page redirects
                sessionStorage.removeItem('redirectAfterLogin');
                sessionStorage.removeItem('originalPageBeforeLogin');

                // Navigate normally
                window.location.replace(redirectUrl);
            }
        } else if (redirectUrl) {
            // Clear the redirect URL
            sessionStorage.removeItem('redirectAfterLogin');

            // Navigate to redirect URL
            window.location.replace(redirectUrl);
        } else {
            // No specific redirect, go to home page
            window.location.replace('/');
        }
    }

    /**
     * Smart back navigation - skips login page
     */
    smartBack() {
        const originalPage = sessionStorage.getItem('originalPageBeforeLogin');
        const currentUrl = window.location.href;

        console.log('🔙 Smart back requested:', { originalPage, currentUrl });

        // If we have an original page stored and we're not on the login page
        if (originalPage && !currentUrl.includes('/login/')) {
            console.log('🔙 Returning to original page:', originalPage);

            // Clear the stored original page
            sessionStorage.removeItem('originalPageBeforeLogin');

            // Navigate to original page
            window.location.href = originalPage;
        } else {
            // Fallback to normal browser back or home page
            if (window.history.length > 1 && document.referrer) {
                console.log('🔙 Using browser back');
                window.history.back();
            } else {
                console.log('🔙 Fallback to home page');
                window.location.href = '/';
            }
        }
    }

    /**
     * Setup history manipulation after successful login redirect
     * This should be called on the target page after login
     */
    setupPostLoginHistory() {
        const originalPage = sessionStorage.getItem('originalPageBeforeLogin');
        const needsHistoryFix = sessionStorage.getItem('needsHistoryFix');
        const currentUrl = window.location.href;

        if (originalPage && needsHistoryFix === 'true') {
            console.log('🔧 Applying immediate redirect to original page');
            console.log('🔧 Original page before login:', originalPage);
            console.log('🔧 Current page after login:', currentUrl);

            // Clear the flags
            sessionStorage.removeItem('needsHistoryFix');
            sessionStorage.removeItem('originalPageBeforeLogin');

            // Immediate redirect to original page using replace
            // This prevents the Contact Us page from fully loading and showing the login message
            console.log('🔄 Immediately redirecting to original page to avoid flash');
            window.location.replace(originalPage);

            return; // Exit early since we're redirecting
        } else if (originalPage) {
            console.log('🔧 Normal post-login setup - no special redirect needed');

            // Clear the stored original page
            sessionStorage.removeItem('originalPageBeforeLogin');
            console.log('🧹 Cleared originalPageBeforeLogin from sessionStorage');
        }
    }

    /**
     * Handle browser back/forward button
     */
    handlePopState(event) {
        // This handles browser back/forward buttons
        // You can add custom logic here if needed
        console.log('🔙 Browser navigation detected');
    }

    /**
     * Clear all stored navigation data
     */
    clearNavigationData() {
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');
        sessionStorage.removeItem('originalPage');
    }

    /**
     * Initialize navigation for buttons that require authentication
     * @param {string} selector - CSS selector for buttons
     * @param {string} targetUrl - URL to navigate to
     */
    initAuthButton(selector, targetUrl) {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateWithAuthCheck(targetUrl, true, button.id);
            });
        });
    }

    /**
     * Initialize smart back buttons
     * @param {string} selector - CSS selector for back buttons
     */
    initSmartBackButtons(selector) {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.smartBack();
            });
        });
    }

    /**
     * Override window.location to intercept JavaScript navigation
     */
    overrideWindowLocation() {
        const originalLocation = window.location;
        let isNavigating = false;

        // Override href setter
        Object.defineProperty(window.location, 'href', {
            set: (url) => {
                if (isNavigating) return;

                if (this.requiresAuthentication(url)) {
                    if (!this.isAuthenticated()) {
                        console.log('🔒 JavaScript navigation requires authentication:', url);
                        this.handleAuthenticatedNavigation(url);
                        return;
                    }
                }

                console.log('🌐 JavaScript navigation allowed:', url);
                isNavigating = true;
                originalLocation.href = url;
            },
            get: () => originalLocation.href
        });
    }
}

// Create global instance
window.globalNav = new GlobalNavigationSystem();
window.navigationManager = window.globalNav; // Backward compatibility

// Expose methods globally for easy access
window.navigateWithAuth = (targetUrl, requiresAuth = false) => {
    window.navigationManager.navigateWithAuthCheck(targetUrl, requiresAuth);
};

window.smartBack = () => {
    window.navigationManager.smartBack();
};

window.handleLoginSuccess = () => {
    window.navigationManager.handleLoginSuccess();
};

window.setupPostLoginHistory = () => {
    window.navigationManager.setupPostLoginHistory();
};

// Global function to handle any button click that might require authentication
window.handleAuthenticatedAction = (targetUrl, requiresAuth = false, actionName = 'navigation') => {
    console.log(`🔘 ${actionName} requested:`, { targetUrl, requiresAuth });

    if (requiresAuth && !window.navigationManager.isAuthenticated()) {
        console.log(`🔒 Authentication required for ${actionName}`);

        // Clear any existing stored URLs
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');

        // Store current page and target
        sessionStorage.setItem('redirectAfterLogin', targetUrl);
        sessionStorage.setItem('originalPageBeforeLogin', window.location.href);

        console.log('📝 Stored authentication context:', {
            redirectAfterLogin: targetUrl,
            originalPageBeforeLogin: window.location.href
        });

        // Redirect to login
        window.location.href = '/login/';
        return false; // Prevent default action
    } else {
        console.log(`✅ ${actionName} allowed, proceeding to:`, targetUrl);

        // Clear any stored URLs since we're navigating directly
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');

        // Navigate to target
        window.location.href = targetUrl;
        return true; // Allow action to proceed
    }
};

// Global function to check if this page was reached after login
window.checkPostLoginSetup = () => {
    const redirectAfterLogin = sessionStorage.getItem('redirectAfterLogin');
    const originalPage = sessionStorage.getItem('originalPageBeforeLogin');
    const currentUrl = window.location.href;

    console.log('🔍 Checking post-login setup:', {
        redirectAfterLogin,
        originalPage,
        currentUrl
    });

    // If we have these values, it means we just came from a login redirect
    if (redirectAfterLogin && originalPage) {
        // Check if current URL matches the redirect URL (normalize URLs for comparison)
        const currentPath = new URL(currentUrl).pathname;
        const redirectPath = new URL(redirectAfterLogin, window.location.origin).pathname;

        if (currentPath === redirectPath) {
            console.log('✅ Detected post-login page load, setting up history');

            // Clear the redirect URL since we're now on the target page
            sessionStorage.removeItem('redirectAfterLogin');

            // Setup history manipulation
            setTimeout(() => {
                window.navigationManager.setupPostLoginHistory();

                // Refresh authentication status on all pages
                if (window.refreshAuthStatus) {
                    window.refreshAuthStatus();
                }
            }, 100); // Small delay to ensure page is fully loaded
        } else {
            console.log('🔍 URL mismatch, not setting up post-login history');
            console.log('Current path:', currentPath);
            console.log('Expected path:', redirectPath);
        }
    }

    // Always refresh auth status when page loads
    setTimeout(() => {
        if (window.refreshAuthStatus) {
            console.log('🔄 Refreshing auth status on page load');
            window.refreshAuthStatus();
        }
    }, 200);
};

// Initialize global authentication check on page load
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔍 Running global authentication check...');
    console.log('🌐 Global Navigation System available:', !!window.globalNav);

    // Test login button detection
    const loginButtons = document.querySelectorAll('[data-login-link="true"], #global-signin-btn, .signin');
    console.log('🔑 Found login buttons:', loginButtons.length, loginButtons);

    // Check if current page requires authentication
    const currentUrl = window.location.href;
    if (window.globalNav && window.globalNav.requiresAuthentication(currentUrl) && !window.globalNav.isAuthenticated()) {
        console.log('🔒 Current page requires authentication, redirecting to login');
        window.globalNav.handleAuthenticatedNavigation(currentUrl);
    }
});

// Add a simple test function
window.testLoginButton = function() {
    console.log('🧪 Testing login button functionality...');
    if (window.globalNav && window.globalNav.handleLoginAction) {
        window.globalNav.handleLoginAction();
    } else {
        console.error('❌ Global navigation system not available');
    }
};

console.log('🚀 Global Navigation System v6.0 initialized with universal authentication checks');
console.log('🧪 Test function available: window.testLoginButton()');
