{% extends 'html/base.html' %}
{% load static %}
{% load custom_tags %}

{% block title %}Account Settings - HiSage Health{% endblock %}

{% block content %}
<!-- Settings Page Styles -->
    <style>
        /* Modern CSS Reset */
        *, *::before, *::after {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* CSS Custom Properties */
        :root {
            --primary-color: #3b82f6;
            --primary-dark: #1e40af;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --surface-color: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #e2e8f0 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Header Section */
        .header {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            padding: 3rem;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            line-height: 1.2;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 1.125rem;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Navigation removed */

        /* Settings Sections */
        .settings-section {
            background: var(--surface-color);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            margin-bottom: 2rem;
            overflow: hidden;
            border: 1px solid var(--border-color);
        }

        .section-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 2rem;
            font-size: 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .section-content {
            padding: 2rem;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 2rem;
        }

        .form-group label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-group label .optional {
            font-weight: 400;
            color: var(--text-muted);
            font-size: 0.75rem;
            margin-left: 0.25rem;
            text-transform: none;
            letter-spacing: normal;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--border-color);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            transition: var(--transition);
            background: var(--surface-color);
            color: var(--text-primary);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-group input[readonly] {
            background: var(--background-color);
            color: var(--text-muted);
            cursor: not-allowed;
        }

        .form-group .help-text {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        /* Avatar Section */
        .avatar-section {
            display: flex;
            align-items: center;
            gap: 2rem;
            margin-bottom: 2rem;
            padding: 2rem;
            background: var(--background-color);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
        }

        .avatar-preview {
            width: 6rem;
            height: 6rem;
            border-radius: 50%;
            border: 3px solid var(--surface-color);
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: 600;
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: var(--transition);
        }

        .avatar-preview:hover {
            transform: scale(1.05);
            box-shadow: var(--shadow-lg);
        }

        .avatar-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-upload {
            flex: 1;
        }

        .file-input-wrapper {
            position: relative;
            display: inline-block;
            cursor: pointer;
        }

        .file-input {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-input-label {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border-radius: var(--radius-lg);
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
        }

        .file-input-label:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 0.875rem 2rem;
            border: none;
            border-radius: var(--radius-lg);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: var(--background-color);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: #e2e8f0;
            color: var(--text-primary);
        }

        .btn-danger {
            background: var(--error-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none !important;
        }

        /* Form Actions */
        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        /* Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .loading i {
            margin-right: 0.75rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Messages */
        .message {
            padding: 1rem 1.5rem;
            border-radius: var(--radius-lg);
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .message.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .message.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .message.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header {
                padding: 2rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .avatar-section {
                flex-direction: column;
                text-align: center;
                gap: 1.5rem;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            /* Back button removed */
        }

        @media (max-width: 480px) {
            .container {
                padding: 0.75rem;
            }
            
            .header {
                padding: 1.5rem;
            }
            
            .section-content {
                padding: 1.5rem;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>

    <!-- Application Configuration -->
    <script>
        // 使用Django模板变量传递配置
        window.API_BASE_URL = "{{ API_BASE_URL }}";
        window.LOCAL_BASE_URL = "{{ LOCAL_BASE_URL }}";
        window.CSRF_TOKEN = "{{ csrf_token }}";

        // 备用配置（如果模板变量为空）
        if (!window.API_BASE_URL || window.API_BASE_URL === '') {
            window.API_BASE_URL = "http://**************:8001";
        }
        if (!window.LOCAL_BASE_URL || window.LOCAL_BASE_URL === '') {
            window.LOCAL_BASE_URL = "http://**************:8000";
        }

        console.log('🔧 Settings page configuration:', {
            API_BASE_URL: window.API_BASE_URL,
            LOCAL_BASE_URL: window.LOCAL_BASE_URL,
            CSRF_TOKEN: window.CSRF_TOKEN ? 'Present' : 'Missing'
        });
    </script>

<!-- Settings page content starts here -->

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Account Settings</h1>
            <p>Manage your account information, preferences, and security settings</p>
        </div>

        <!-- Messages Container -->
        <div id="messages-container"></div>

        <!-- Personal Information Section -->
        <div class="settings-section">
            <div class="section-header">
                <i class="fas fa-user" aria-hidden="true"></i>
                <span>Personal Information</span>
            </div>
            <div class="section-content">
                <!-- Avatar Section -->
                <div class="avatar-section">
                    <div id="avatar-preview" class="avatar-preview" role="img" aria-label="Current avatar">
                        <i class="fas fa-user" aria-hidden="true"></i>
                    </div>
                    <div class="avatar-upload">
                        <h4>Profile Picture</h4>
                        <p class="help-text">Upload a new profile picture. Recommended size: 200x200px</p>
                        <div class="file-input-wrapper">
                            <input type="file" id="avatar-input" class="file-input" accept="image/*" aria-label="Upload new avatar">
                            <label for="avatar-input" class="file-input-label">
                                <i class="fas fa-upload" aria-hidden="true"></i>
                                <span>Choose Image</span>
                            </label>
                        </div>

                    </div>
                </div>

                <form id="personal-info-form">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" name="email" readonly aria-describedby="email-help">
                        <div id="email-help" class="help-text">Email cannot be changed.</div>
                    </div>

                    <div class="form-group">
                        <label for="first-name">First Name <span class="optional">(Optional)</span></label>
                        <input type="text" id="first-name" name="first_name" placeholder="Enter your first name">
                    </div>

                    <div class="form-group">
                        <label for="last-name">Last Name <span class="optional">(Optional)</span></label>
                        <input type="text" id="last-name" name="last_name" placeholder="Enter your last name">
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetPersonalInfo()">
                            <i class="fas fa-undo" aria-hidden="true"></i>
                            <span>Reset</span>
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save" aria-hidden="true"></i>
                            <span>Save Changes</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Password Section -->
        <div class="settings-section">
            <div class="section-header">
                <i class="fas fa-lock" aria-hidden="true"></i>
                <span>Password & Security</span>
            </div>
            <div class="section-content">
                <form id="password-form">
                    <div class="form-group">
                        <label for="current-password">Current Password</label>
                        <input type="password" id="current-password" name="current_password" placeholder="Enter your current password" required>
                    </div>

                    <div class="form-group">
                        <label for="new-password">New Password</label>
                        <input type="password" id="new-password" name="new_password" placeholder="Enter new password" required>
                        <div class="help-text">Password must be at least 8 characters long and contain letters and numbers.</div>
                    </div>

                    <div class="form-group">
                        <label for="confirm-password">Confirm New Password</label>
                        <input type="password" id="confirm-password" name="confirm_password" placeholder="Confirm new password" required>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="resetPasswordForm()">
                            <i class="fas fa-undo" aria-hidden="true"></i>
                            <span>Reset</span>
                        </button>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-key" aria-hidden="true"></i>
                            <span>Update Password</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Modern Settings Application Script -->
    <script>
        /**
         * Modern Settings Application
         * High-performance account settings management
         */
        class SettingsApp {
            constructor() {
                this.config = {
                    apiBaseUrl: window.API_BASE_URL,
                    localBaseUrl: window.LOCAL_BASE_URL
                };

                this.cache = new Map();
                this.isLoading = false;

                this.init();
            }

            async init() {
                try {
                    // 首先检查认证状态
                    const token = this.getAuthToken();
                    if (!token) {
                        console.warn('⚠️ No authentication token found');
                        this.showMessage('Please log in to access settings. Redirecting...', 'warning');
                        setTimeout(() => {
                            window.location.href = '/login/';
                        }, 2000);
                        return;
                    }

                    if (this.isTokenExpired(token)) {
                        console.warn('⚠️ Authentication token expired');
                        this.showMessage('Your session has expired. Please log in again.', 'warning');
                        localStorage.removeItem('access_token');
                        setTimeout(() => {
                            window.location.href = '/login/';
                        }, 2000);
                        return;
                    }

                    console.log('✅ Authentication token valid, loading user data...');
                    await this.loadUserData();
                    this.setupEventListeners();
                } catch (error) {
                    console.error('Failed to initialize settings:', error);
                    this.showMessage('Failed to load settings', 'error');
                }
            }

            // Authentication helpers
            getAuthToken() {
                return localStorage.getItem('access_token');
            }

            isTokenExpired(token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    return payload.exp < Date.now() / 1000;
                } catch {
                    return true;
                }
            }

            async makeAuthenticatedRequest(url, options = {}) {
                const token = this.getAuthToken();

                console.log('🔐 Making authenticated request to:', url);
                console.log('🔑 Token present:', !!token);

                if (!token) {
                    console.warn('⚠️ No auth token found, redirecting to login');
                    window.location.href = '/login/';
                    return null;
                }

                if (this.isTokenExpired(token)) {
                    console.warn('⚠️ Token expired, redirecting to login');
                    localStorage.removeItem('access_token');
                    window.location.href = '/login/';
                    return null;
                }

                try {
                    const response = await fetch(url, {
                        ...options,
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json',
                            ...options.headers
                        }
                    });

                    console.log('📡 Response received:', response.status, response.statusText);

                    if (response.status === 401) {
                        console.warn('⚠️ 401 Unauthorized, clearing token and redirecting');
                        localStorage.removeItem('access_token');
                        window.location.href = '/login/';
                        return null;
                    }

                    return response;
                } catch (error) {
                    console.error('❌ Network error in authenticated request:', error);
                    throw error;
                }
            }

            // Load user data
            async loadUserData() {
                try {
                    console.log('🔄 Loading user data from:', `${this.config.apiBaseUrl}/api/user/profile/`);

                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/user/profile/`
                    );

                    if (!response) {
                        throw new Error('No response received');
                    }

                    console.log('📡 Response status:', response.status);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('✅ User data received:', data);

                        // 处理不同的响应格式
                        const userData = data.data || data.user || data;
                        this.populateUserData(userData);
                        this.cache.set('userData', data);
                    } else {
                        const errorText = await response.text();
                        console.error('❌ API Error:', errorText);

                        // 尝试使用备用数据
                        console.warn('⚠️ Using fallback user data');
                        const fallbackData = {
                            user: {
                                email: '<EMAIL>',
                                first_name: 'Current',
                                last_name: 'User',
                                avatar: null
                            }
                        };
                        this.populateUserData(fallbackData.user);
                        this.showMessage('Using fallback user data - some features may be limited', 'warning');
                        return;
                    }
                } catch (error) {
                    console.error('❌ Failed to load user data:', error);

                    // 网络错误时也使用备用数据
                    console.warn('⚠️ Network error, using fallback user data');
                    const fallbackData = {
                        user: {
                            email: '<EMAIL>',
                            first_name: 'Current',
                            last_name: 'User',
                            avatar: null
                        }
                    };
                    this.populateUserData(fallbackData.user);
                    this.showMessage('Network error - using fallback data. Please check your connection.', 'warning');
                }
            }

            // Populate form with user data
            populateUserData(user) {
                try {
                    console.log('📝 Populating user data:', user);

                    const emailField = document.getElementById('email');
                    const firstNameField = document.getElementById('first-name');
                    const lastNameField = document.getElementById('last-name');

                    if (emailField) emailField.value = user.email || '';
                    if (firstNameField) firstNameField.value = user.first_name || '';
                    if (lastNameField) lastNameField.value = user.last_name || '';

                    // Update avatar - 处理前端dp字段和后端avatar字段
                    let avatarUrl = user.avatar || user.dp || null;

                    // 确保头像URL是完整的
                    if (avatarUrl && avatarUrl.startsWith('/media/')) {
                        avatarUrl = this.config.apiBaseUrl + avatarUrl;
                    }

                    this.updateAvatarPreview(avatarUrl, user.first_name, user.last_name, user.email);

                    console.log('✅ User data populated successfully');
                } catch (error) {
                    console.error('❌ Error populating user data:', error);
                    this.showMessage('Error displaying user information', 'error');
                }
            }

            // Update avatar preview
            updateAvatarPreview(avatarUrl, firstName, lastName, email) {
                try {
                    const preview = document.getElementById('avatar-preview');

                    if (!preview) {
                        console.error('❌ Avatar preview element not found');
                        return;
                    }

                    console.log('🖼️ Updating avatar preview:', {
                        avatarUrl: avatarUrl,
                        hasPreviewElement: !!preview
                    });

                    if (avatarUrl && avatarUrl !== 'null' && avatarUrl !== '') {
                        // 创建新的img元素
                        const img = document.createElement('img');
                        img.src = avatarUrl;
                        img.alt = 'User avatar';
                        img.loading = 'lazy';
                        img.style.width = '100%';
                        img.style.height = '100%';
                        img.style.objectFit = 'cover';
                        img.style.borderRadius = '50%';

                        // 添加加载事件监听器
                        img.onload = () => {
                            console.log('✅ Avatar image loaded successfully');
                        };

                        img.onerror = () => {
                            console.error('❌ Failed to load avatar image:', avatarUrl);
                            // 如果图片加载失败，显示默认头像
                            this.showDefaultAvatar(preview, firstName, lastName, email);
                        };

                        // 清空预览容器并添加新图片
                        preview.innerHTML = '';
                        preview.appendChild(img);

                        console.log('✅ Avatar preview updated with image');
                    } else {
                        console.log('📝 No avatar URL, showing initials');
                        this.showDefaultAvatar(preview, firstName, lastName, email);
                    }
                } catch (error) {
                    console.error('❌ Error updating avatar preview:', error);
                }
            }

            // Show default avatar with initials
            showDefaultAvatar(preview, firstName, lastName, email) {
                const initials = this.getInitials(firstName, lastName, email);
                preview.innerHTML = `
                    <div style="
                        width: 100%;
                        height: 100%;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 2rem;
                        font-weight: bold;
                        border-radius: 50%;
                    ">${initials}</div>
                `;
            }

            // Refresh all avatar elements on the page
            refreshAllAvatars() {
                try {
                    console.log('🔄 Refreshing all avatar elements...');

                    // 查找页面上所有可能的头像元素
                    const avatarSelectors = [
                        '#avatar-preview img',
                        '.avatar img',
                        '.user-avatar img',
                        '[data-avatar] img'
                    ];

                    avatarSelectors.forEach(selector => {
                        const avatars = document.querySelectorAll(selector);
                        avatars.forEach(img => {
                            if (img.src && img.src.includes('/media/')) {
                                // 移除旧的时间戳并添加新的
                                const baseUrl = img.src.split('?')[0];
                                img.src = baseUrl + '?t=' + Date.now();
                                console.log('🔄 Refreshed avatar:', img.src);
                            }
                        });
                    });
                } catch (error) {
                    console.error('❌ Error refreshing avatars:', error);
                }
            }

            // Get user initials
            getInitials(firstName, lastName, email) {
                if (firstName && lastName) {
                    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
                } else if (firstName) {
                    return firstName.charAt(0).toUpperCase();
                } else {
                    return email.charAt(0).toUpperCase();
                }
            }

            // Setup event listeners
            setupEventListeners() {
                // Personal info form
                document.getElementById('personal-info-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updatePersonalInfo();
                });

                // Password form
                document.getElementById('password-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.updatePassword();
                });

                // Avatar upload
                document.getElementById('avatar-input').addEventListener('change', (e) => {
                    this.handleAvatarUpload(e);
                });

                // Password confirmation validation
                document.getElementById('confirm-password').addEventListener('input', (e) => {
                    this.validatePasswordConfirmation();
                });
            }

            // Update personal information
            async updatePersonalInfo() {
                if (this.isLoading) return;
                this.isLoading = true;

                const formData = new FormData(document.getElementById('personal-info-form'));
                const data = Object.fromEntries(formData.entries());

                try {
                    console.log('🔄 Updating user profile:', data);

                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/user/profile/`,
                        {
                            method: 'PUT',
                            body: JSON.stringify(data)
                        }
                    );

                    if (!response) return;

                    if (response.ok) {
                        this.showMessage('Personal information updated successfully', 'success');
                        const updatedData = await response.json();
                        this.cache.set('userData', updatedData);
                    } else {
                        const errorData = await response.json();
                        throw new Error(errorData.message || 'Failed to update information');
                    }
                } catch (error) {
                    console.error('Failed to update personal info:', error);
                    this.showMessage(error.message || 'Failed to update personal information', 'error');
                } finally {
                    this.isLoading = false;
                }
            }

            // Update password
            async updatePassword() {
                if (this.isLoading) return;

                const currentPassword = document.getElementById('current-password').value;
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;

                console.log('🔍 Password form values:', {
                    currentPassword: currentPassword ? 'Present' : 'Empty',
                    newPassword: newPassword ? 'Present' : 'Empty',
                    confirmPassword: confirmPassword ? 'Present' : 'Empty'
                });

                // Validate required fields
                if (!currentPassword || !currentPassword.trim()) {
                    this.showMessage('Please enter your current password', 'error');
                    return;
                }

                if (!newPassword || !newPassword.trim()) {
                    this.showMessage('Please enter a new password', 'error');
                    return;
                }

                if (!confirmPassword || !confirmPassword.trim()) {
                    this.showMessage('Please confirm your new password', 'error');
                    return;
                }

                // Validate passwords
                if (newPassword !== confirmPassword) {
                    this.showMessage('New passwords do not match', 'error');
                    return;
                }

                if (newPassword.length < 8) {
                    this.showMessage('Password must be at least 8 characters long', 'error');
                    return;
                }

                this.isLoading = true;

                try {
                    console.log('🔄 Changing password...');

                    const requestData = {
                        current_password: currentPassword,
                        new_password: newPassword
                    };

                    console.log('📤 Sending password change request:', {
                        current_password: 'Present',
                        new_password: 'Present'
                    });

                    const response = await this.makeAuthenticatedRequest(
                        `${this.config.apiBaseUrl}/api/user/change-password/`,
                        {
                            method: 'POST',
                            body: JSON.stringify(requestData)
                        }
                    );

                    if (!response) return;

                    console.log('📡 Password change response:', response.status);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('✅ Password changed successfully:', data);
                        this.showMessage('Password updated successfully', 'success');
                        document.getElementById('password-form').reset();
                    } else {
                        let errorMessage = 'Failed to update password';
                        try {
                            const errorData = await response.json();
                            console.error('❌ Password change error:', errorData);
                            errorMessage = errorData.message || errorData.error || errorMessage;
                        } catch (parseError) {
                            console.error('❌ Could not parse error response');
                            const errorText = await response.text();
                            console.error('❌ Raw error response:', errorText);
                            errorMessage = `Server error (${response.status})`;
                        }
                        throw new Error(errorMessage);
                    }
                } catch (error) {
                    console.error('❌ Failed to update password:', error);
                    this.showMessage(error.message || 'Failed to update password', 'error');
                } finally {
                    this.isLoading = false;
                }
            }

            // Handle avatar upload
            async handleAvatarUpload(event) {
                const file = event.target.files[0];
                if (!file) return;

                // Validate file type
                if (!file.type.startsWith('image/')) {
                    this.showMessage('Please select a valid image file', 'error');
                    return;
                }

                // Validate file size (max 5MB)
                if (file.size > 5 * 1024 * 1024) {
                    this.showMessage('Image size must be less than 5MB', 'error');
                    return;
                }

                // Preview image
                const reader = new FileReader();
                reader.onload = (e) => {
                    const preview = document.getElementById('avatar-preview');
                    preview.innerHTML = `<img src="${e.target.result}" alt="Avatar preview" loading="lazy">`;
                };
                reader.readAsDataURL(file);

                // Upload image
                await this.uploadAvatar(file);
            }

            // Upload avatar to server
            async uploadAvatar(file) {
                if (this.isLoading) return;
                this.isLoading = true;

                const formData = new FormData();
                formData.append('avatar', file);

                try {
                    console.log('🔄 Uploading avatar...');
                    console.log('📁 File details:', {
                        name: file.name,
                        size: file.size,
                        type: file.type
                    });

                    // 使用后端API进行头像上传
                    const uploadUrl = `${this.config.apiBaseUrl}/api/user/profile/`;
                    console.log('🌐 Upload URL:', uploadUrl);

                    const token = this.getAuthToken();
                    if (!token) {
                        throw new Error('No authentication token found. Please log in again.');
                    }
                    console.log('🔑 Auth Token:', token ? 'Present' : 'Missing');

                    const response = await fetch(uploadUrl, {
                        method: 'PUT',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        },
                        body: formData
                    });

                    console.log('📡 Avatar upload response:', response.status);

                    if (response.ok) {
                        const data = await response.json();
                        console.log('✅ Avatar upload successful:', data);
                        console.log('📊 Full response data structure:', JSON.stringify(data, null, 2));

                        this.showMessage('Avatar updated successfully', 'success');

                        // 从后端响应中获取头像URL - 检查多种可能的字段
                        let avatarUrl = null;
                        if (data.user && data.user.avatar) {
                            avatarUrl = data.user.avatar;
                        } else if (data.avatar_url) {
                            avatarUrl = data.avatar_url;
                        } else if (data.avatar) {
                            avatarUrl = data.avatar;
                        }

                        console.log('🖼️ Extracted avatar URL:', avatarUrl);

                        // 如果有头像URL，立即更新预览
                        if (avatarUrl) {
                            // 确保URL是完整的
                            let fullAvatarUrl = avatarUrl;
                            if (avatarUrl.startsWith('/media/')) {
                                fullAvatarUrl = this.config.apiBaseUrl + avatarUrl;
                            }

                            // 添加时间戳避免浏览器缓存
                            const avatarUrlWithTimestamp = fullAvatarUrl + '?t=' + Date.now();
                            console.log('🔄 Updating avatar preview with URL:', avatarUrlWithTimestamp);

                            this.updateAvatarPreview(avatarUrlWithTimestamp, null, null, null);

                            // 更新缓存的用户数据
                            const userData = this.cache.get('userData');
                            if (userData) {
                                userData.user.avatar = avatarUrl; // 缓存中不需要时间戳
                                this.cache.set('userData', userData);
                                console.log('💾 Updated cached user data');
                            }
                        } else {
                            console.warn('⚠️ No avatar URL found in response');
                        }

                        // 强制刷新页面上的所有头像元素
                        this.refreshAllAvatars();

                        // 重新加载用户数据以确保同步
                        setTimeout(() => {
                            console.log('🔄 Reloading user data to sync...');
                            this.loadUserData();
                        }, 1000);
                    } else {
                        // 尝试解析错误响应
                        let errorMessage = 'Failed to upload avatar';
                        let responseContent = '';

                        try {
                            // 首先尝试获取响应内容
                            responseContent = await response.text();
                            console.error('❌ Error response content:', responseContent);

                            // 尝试解析为JSON
                            const errorData = JSON.parse(responseContent);
                            errorMessage = errorData.message || errorData.error || errorMessage;
                        } catch (parseError) {
                            console.error('❌ Failed to parse error response as JSON:', parseError);

                            // 如果响应内容看起来像HTML（通常是Django错误页面）
                            if (responseContent.includes('<html>') || responseContent.includes('<!DOCTYPE')) {
                                errorMessage = `Server error (${response.status}): Please check server logs`;
                            } else if (responseContent) {
                                errorMessage = `Server error (${response.status}): ${responseContent.substring(0, 100)}`;
                            } else {
                                errorMessage = `Server error (${response.status})`;
                            }
                        }
                        throw new Error(errorMessage);
                    }
                } catch (error) {
                    console.error('Failed to upload avatar:', error);

                    // 特殊处理认证错误
                    if (error.message.includes('Authentication required') ||
                        error.message.includes('401') ||
                        error.message.includes('Unauthorized')) {
                        this.showMessage('Please log in to upload avatar. Redirecting to login...', 'error');
                        setTimeout(() => {
                            window.location.href = '/login/';
                        }, 2000);
                        return;
                    }

                    this.showMessage(error.message || 'Failed to upload avatar', 'error');
                    if (userData) {
                        this.updateAvatarPreview(
                            userData.user?.avatar,
                            userData.user?.first_name,
                            userData.user?.last_name,
                            userData.user?.email
                        );
                    }
                } finally {
                    this.isLoading = false;
                }
            }

            // Validate password confirmation
            validatePasswordConfirmation() {
                const newPassword = document.getElementById('new-password').value;
                const confirmPassword = document.getElementById('confirm-password').value;
                const confirmInput = document.getElementById('confirm-password');

                if (confirmPassword && newPassword !== confirmPassword) {
                    confirmInput.style.borderColor = 'var(--error-color)';
                } else {
                    confirmInput.style.borderColor = 'var(--border-color)';
                }
            }

            // Show message to user
            showMessage(message, type = 'info') {
                const container = document.getElementById('messages-container');
                const messageEl = document.createElement('div');
                messageEl.className = `message ${type}`;

                const icon = type === 'success' ? 'check-circle' :
                           type === 'error' ? 'exclamation-triangle' :
                           type === 'warning' ? 'exclamation-circle' : 'info-circle';

                messageEl.innerHTML = `
                    <i class="fas fa-${icon}" aria-hidden="true"></i>
                    <span>${this.escapeHtml(message)}</span>
                `;

                container.appendChild(messageEl);

                // Auto-remove after 5 seconds
                setTimeout(() => {
                    if (messageEl.parentNode) {
                        messageEl.parentNode.removeChild(messageEl);
                    }
                }, 5000);

                // Scroll to message
                messageEl.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            }

            // Utility methods
            escapeHtml(text) {
                if (!text) return '';
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Global utility functions
        function resetPersonalInfo() {
            const userData = window.settingsApp?.cache.get('userData');
            if (userData) {
                window.settingsApp.populateUserData(userData.user || userData);
            }
        }

        function resetPasswordForm() {
            document.getElementById('password-form').reset();
        }



        // Initialize application
        let settingsApp;
        document.addEventListener('DOMContentLoaded', () => {
            try {
                settingsApp = new SettingsApp();
                window.settingsApp = settingsApp; // For debugging
            } catch (error) {
                console.error('Failed to initialize settings app:', error);
            }
        });
    </script>
{% endblock %}
