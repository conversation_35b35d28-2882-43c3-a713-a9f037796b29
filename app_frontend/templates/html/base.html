{% load static %}
{% load tailwind_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}HiSage - AI-Powered Dementia Screening Platform{% endblock %}</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="{% block description %}HiSage Health - AI-powered cognitive health analysis platform providing professional cognitive health assessment through speech analysis{% endblock %}">
    <meta name="keywords" content="cognitive health,AI analysis,speech analysis,health assessment,HiSage Health">
    <meta name="author" content="HiSage Health Team">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}HiSage Health - AI认知健康分析平台{% endblock %}">
    <meta property="og:description" content="{% block og_description %}基于AI的认知健康分析平台，通过语音分析提供专业的认知健康评估{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:image" content="{% static 'assets/logos/logo1.png' %}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{% static 'assets/logos/logo1.png' %}">
    
    <!-- CSS Dependencies -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    {% tailwind_css %}
    <link rel="stylesheet" href="{% static 'css/index.css' %}">
    
    <style>
        /* 基础样式 */
        .header-links {
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer-link {
            text-decoration: none;
            color: black;
        }
        
        .footer-link:hover {
            color: #2da44e;
        }
        
        /* 现代化用户Profile样式 */
        .user-profile {
            position: relative;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .profile-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            object-fit: cover;
        }
        
        .profile-avatar:hover {
            border-color: #3b82f6;
            transform: scale(1.05);
        }

        .user-profile {
            position: relative;
            display: inline-flex;
            align-items: center;
            cursor: pointer;
            padding: 4px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .user-profile:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .profile-avatar-text {
            display: none;
        }

        .profile-avatar-text:hover {
            transform: scale(1.05);
        }
        
        .profile-dropdown {
            position: fixed;
            top: 68px; /* 与通知面板完全一致 */
            right: 20px;
            min-width: 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
            border: 1px solid #e5e7eb;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.2s ease;
            z-index: 9500; /* 与通知面板相同的z-index */
        }
        
        .profile-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .profile-header {
            padding: 20px;
            border-bottom: 1px solid #f3f4f6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px 12px 0 0;
            color: white;
        }
        
        .profile-info {
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .profile-info .profile-avatar-text {
            position: relative;
        }

        .profile-avatar-large {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            object-fit: cover;
        }
        
        .profile-details h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .profile-details p {
            margin: 4px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .profile-menu {
            padding: 8px 0;
        }
        
        .profile-menu-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.15s ease;
        }
        
        .profile-menu-item:hover {
            background-color: #f9fafb;
            color: #3b82f6;
        }
        
        .profile-menu-item i {
            width: 16px;
            text-align: center;
            font-size: 16px;
        }
        
        .profile-divider {
            height: 1px;
            background-color: #f3f4f6;
            margin: 8px 0;
        }
        
        .profile-menu-item.danger {
            color: #ef4444;
        }
        
        .profile-menu-item.danger:hover {
            background-color: #fef2f2;
            color: #dc2626;
        }
        
        .auth-buttons {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .auth-btn {
            padding: 8px 16px;
            border-radius: 8px;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .auth-btn.signin {
            color: #374151;
            border: 1px solid #d1d5db;
        }
        
        .auth-btn.signin:hover {
            background-color: #f9fafb;
            border-color: #9ca3af;
        }
        
        .auth-btn.signup {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        
        .auth-btn.signup:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        /* 移除遮罩层 - 与通知面板保持一致 */

        /* 响应式设计 */
        @media (max-width: 768px) {
            .profile-dropdown {
                min-width: 260px;
                right: 10px !important;
                left: 10px !important;
                width: calc(100% - 20px);
                max-width: 320px;
            }

            .profile-header {
                padding: 16px;
            }

            .profile-avatar-large {
                width: 40px;
                height: 40px;
            }
        }

        /* 通知铃铛样式 */
        .notification-bell {
            position: relative; /* 保持relative定位，作为通知面板的定位容器 */
            cursor: pointer;
            padding: 8px;
            margin-right: 12px;
            border-radius: 50%;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-bell:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        .notification-bell i {
            font-size: 20px;
            color: #6b7280;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: #ef4444;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            font-size: 11px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* 通知面板样式 - 不再需要遮罩层 */

        .user-profile {
            position: relative; /* 确保user-profile是定位容器 */
        }



        /* 通知面板 - 无箭头设计 */

        /* Profile dropdown z-index */
        .profile-dropdown {
            z-index: 9500 !important;
        }


    </style>
</head>

<body class="tw-bg-gray-50">
    <!-- 通知栏 -->
    <div id="notification-bar" class="tw-hidden tw-fixed tw-top-0 tw-left-0 tw-w-full tw-bg-blue-600 tw-text-white tw-py-2 tw-px-4 tw-z-50">
        <div class="tw-flex tw-justify-between tw-items-center tw-max-w-6xl tw-mx-auto">
          <span id="notification-text"></span>
          <button type="button" class="bi bi-x tw-text-lg" aria-label="Close"></button>
        </div>
    </div>

    <!-- 导航栏 -->
    <header class="tw-flex tw-w-full tw-z-20 tw-h-[60px] lg:tw-justify-around tw-absolute tw-top-0 tw-px-[10%] max-lg:tw-mr-auto tw-bg-white tw-shadow-sm">
        <!-- Logo -->
        <a class="tw-w-[60px] tw-h-[60px] tw-p-[4px] tw-flex tw-items-center" href="{% url 'home' %}">
            <img src="{% static 'assets/logos/logo1.png' %}" alt="HiSage Health Logo" class="tw-w-full tw-h-full tw-object-contain">
        </a>
        
        <!-- 导航内容 -->
        <div class="collapsable-header animated-collapse max-lg:tw-shadow-md" id="collapsed-header-items">
            <!-- 导航链接 -->
            <div class="tw-w-max tw-text-base tw-flex tw-gap-5 tw-h-full lg:tw-mx-auto lg:tw-place-items-center max-lg:tw-flex-col max-lg:tw-w-full max-lg:tw-mt-[50px] max-lg:tw-gap-5 tw-text-black">
                <a class="header-links" href="{% url 'about' %}">About us</a>
                <a class="header-links" href="{% url 'contact-us' %}" rel="noreferrer">Contact us</a>
                <a class="header-links" href="{% url 'message_board' %}" rel="noreferrer">
                    <i class="fas fa-comments"></i> Message Board
                </a>
            </div>
            
            <!-- 右侧按钮区域 -->
            <div class="tw-flex tw-gap-5 tw-place-items-center max-lg:tw-flex-col max-lg:tw-w-full max-lg:tw-gap-5">
                <!-- 社交媒体链接 -->
                <div class="tw-flex tw-gap-3">
                    <a href="https://www.instagram.com/hisage.health/" target="_blank" rel="noreferrer" aria-label="instagram" class="header-links tw-transition-colors tw-duration-[0.3s]">
                        <i class="bi bi-instagram"></i>
                    </a>
                    <a href="https://twitter.com/HiSageMain" target="_blank" rel="noreferrer" aria-label="twitter" class="header-links tw-transition-colors tw-duration-[0.3s]">
                        <i class="bi bi-twitter"></i>
                    </a>
                </div>

                <!-- 认证按钮/用户Profile -->
                <div id="auth-section">
                    <!-- 未登录状态 -->
                    <div id="auth-buttons" class="auth-buttons">
                        <a href="#" class="auth-btn signin" id="global-signin-btn">Sign in</a>
                        <a href="{% url 'register' %}" class="auth-btn signup">Sign up</a>
                    </div>

                    <!-- 已登录状态 -->
                    <div id="user-profile" class="user-profile" style="display: none;">
                        <!-- 通知铃铛 -->
                        <div class="notification-bell" id="notification-bell" onclick="goToNotifications(event)">
                            <i class="bi bi-bell"></i>
                            <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
                        </div>
                        <!-- 用户头像 -->
                        <img id="profile-avatar" src="{% static 'assets/images/default-avatar.svg' %}" alt="User Avatar" class="profile-avatar">


                    </div>
                </div>
            </div>
        </div>

        <!-- 移动端菜单按钮 -->
        <button class="tw-absolute tw-text-black tw-z-50 tw-right-3 tw-top-3 tw-text-3xl bi bi-list lg:tw-hidden"
                onclick="toggleHeader()" aria-label="menu" id="collapse-btn">
        </button>
    </header>



    <!-- Profile下拉菜单（独立于header，用于定位） -->
    <div id="profile-dropdown" class="profile-dropdown">
        <!-- Profile Header -->
        <div class="profile-header">
            <div class="profile-info">
                <img id="profile-avatar-large" src="{% static 'assets/images/default-avatar.svg' %}" alt="User Avatar" class="profile-avatar-large">
                <div class="profile-details">
                    <h3 id="profile-name">User Name</h3>
                    <p id="profile-email"><EMAIL></p>
                </div>
            </div>
        </div>
        
        <!-- Menu Items -->
        <div class="profile-menu">
            <a href="/user/profile/" class="profile-menu-item">
                <i class="bi bi-person"></i>
                <span>Profile</span>
            </a>
            <a href="/audio_upload/history/" class="profile-menu-item">
                <i class="bi bi-clock-history"></i>
                <span>Speech Analysis History</span>
            </a>
            <a href="/user/settings/" class="profile-menu-item">
                <i class="bi bi-gear"></i>
                <span>Settings</span>
            </a>
            <!-- Admin-only links -->
            <div id="admin-menu-items" style="display: none;">
                <div class="profile-divider"></div>
                <a href="/dashboard/"
                   class="profile-menu-item"
                   data-auth-required="true"
                   data-action-name="access dashboard">
                    <i class="bi bi-speedometer2"></i>
                    <span>Dashboard</span>
                </a>
                <a href="/message/"
                   class="profile-menu-item"
                   data-auth-required="true"
                   data-action-name="manage messages">
                    <i class="bi bi-envelope"></i>
                    <span>Messages</span>
                </a>
            </div>
            <div class="profile-divider"></div>
            <a href="#" id="logout-btn" class="profile-menu-item danger">
                <i class="bi bi-box-arrow-right"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="tw-w-full tw-h-full tw-mt-[50px]">
        {% block content %}{% endblock content %}
    </div>

    <!-- 页脚 -->
    <footer class="tw-flex max-md:tw-flex-col tw-w-full tw-p-[5%] tw-bg-gray-100 tw-mt-20">
        {% block footer %}
        <div class="tw-flex tw-flex-col tw-gap-5 tw-w-full">
            <div class="tw-flex max-md:tw-flex-col tw-gap-10 tw-w-full">
                <div class="tw-flex tw-flex-col tw-gap-3 tw-w-full">
                    <h3 class="tw-text-lg tw-font-semibold">HiSage</h3>
                    <p class="tw-text-gray-600">AI-Powered Dementia Screening Platform</p>
                </div>
                
                <div class="tw-flex tw-flex-col tw-gap-3 tw-w-full">
                    <h4 class="tw-font-semibold">Quick Links</h4>
                    <a href="{% url 'about' %}" class="footer-link">About us</a>
                    <a href="{% url 'contact-us' %}" class="footer-link">Contact us</a>
                </div>

                <div class="tw-flex tw-flex-col tw-gap-3 tw-w-full">
                    <h4 class="tw-font-semibold">Follow Us</h4>
                    <a href="https://twitter.com/HiSageMain" target="_blank" rel="noreferrer" class="footer-link tw-flex tw-items-center tw-gap-2">
                        <i class="bi bi-twitter"></i>
                        Twitter
                    </a>
                    <a href="https://www.instagram.com/hisage.health/" target="_blank" rel="noreferrer" class="footer-link tw-flex tw-items-center tw-gap-2">
                        <i class="bi bi-instagram"></i>
                        Instagram
                    </a>
                </div>
            </div>
            
            <div class="tw-border-t tw-pt-5 tw-text-center tw-text-gray-600">
                <p>&copy; 2025 HiSage. All rights reserved.</p>
            </div>
        </div>
        {% endblock footer %}
    </footer>

    <!-- JavaScript Dependencies -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.9.2/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- API配置 -->
    <script>
        // 从Django传递API配置到前端
        window.API_CONFIG = {
            API_BASE_URL: '{{ API_BASE_URL }}/api',
            LOCAL_BASE_URL: '{{ LOCAL_BASE_URL }}'
        };
    </script>

    <!-- 用户Profile功能 -->
    <script>
        class UserProfile {
            constructor() {
                this.authButtons = document.getElementById('auth-buttons');
                this.userProfile = document.getElementById('user-profile');
                this.profileAvatar = document.getElementById('profile-avatar');
                this.profileAvatarLarge = document.getElementById('profile-avatar-large');
                this.profileDropdown = document.getElementById('profile-dropdown');
                // 移除遮罩层引用 - 与通知面板保持一致
                this.profileName = document.getElementById('profile-name');
                this.profileEmail = document.getElementById('profile-email');
                this.logoutBtn = document.getElementById('logout-btn');

                this.isDropdownOpen = false;
                this.init();
            }

            init() {
                // 检查登录状态
                this.checkAuthStatus();

                // 绑定事件
                this.bindEvents();
            }

            bindEvents() {
                // 点击整个用户profile区域切换下拉菜单（包括头像和文字头像）
                this.userProfile?.addEventListener('click', (e) => {
                    // 如果点击的是通知铃铛或其子元素，不处理profile点击
                    if (e.target.closest('.notification-bell')) {
                        return; // 让通知铃铛的点击事件正常处理
                    }

                    e.stopPropagation();
                    console.log('🖱️ User profile clicked, toggling dropdown');
                    this.toggleDropdown();
                });

                // 点击遮罩层关闭下拉菜单
                this.profileOverlay?.addEventListener('click', () => {
                    this.closeDropdown();
                });

                // 点击页面其他地方关闭下拉菜单
                document.addEventListener('click', (e) => {
                    // 如果点击的是通知相关元素，不关闭profile下拉菜单
                    if (e.target.closest('.notification-bell') ||
                        e.target.closest('#notification-panel')) {
                        return;
                    }

                    if (!e.target.closest('#user-profile') && !e.target.closest('#profile-dropdown')) {
                        this.closeDropdown();
                    }
                });

                // ESC键关闭下拉菜单
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        this.closeDropdown();
                    }
                });

                // 登出按钮
                this.logoutBtn?.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.logout();
                });

                // 窗口大小改变时重新定位
                window.addEventListener('resize', () => {
                    if (this.isDropdownOpen) {
                        this.positionDropdown();
                    }
                });
            }

            toggleDropdown() {
                console.log('🔄 Toggling dropdown, current state:', this.isDropdownOpen);
                if (this.isDropdownOpen) {
                    this.closeDropdown();
                } else {
                    this.openDropdown();
                }
            }

            openDropdown() {
                console.log('📂 Opening dropdown');

                // 关闭通知面板（如果打开）
                if (window.notificationSystem && window.notificationSystem.isOpen) {
                    closeNotifications();
                }

                this.isDropdownOpen = true;
                this.profileDropdown.classList.add('show');

                // 设置aria-expanded属性到用户profile容器而不是特定的头像元素
                this.userProfile.setAttribute('aria-expanded', 'true');
                this.positionDropdown();
            }

            closeDropdown() {
                console.log('📁 Closing dropdown');
                this.isDropdownOpen = false;
                this.profileDropdown.classList.remove('show');

                // 设置aria-expanded属性到用户profile容器
                this.userProfile.setAttribute('aria-expanded', 'false');
            }

            positionDropdown() {
                console.log('📍 Positioning dropdown');

                // 获取实际显示的头像元素位置
                let avatarElement = null;
                let rect = null;

                // 检查是否有显示的图片头像
                if (this.profileAvatar.style.display !== 'none') {
                    avatarElement = this.profileAvatar;
                    rect = avatarElement.getBoundingClientRect();
                    console.log('📍 Using image avatar for positioning');
                } else {
                    // 查找显示的文字头像
                    const textAvatar = document.querySelector('.profile-avatar-text-small');
                    if (textAvatar && textAvatar.style.display !== 'none') {
                        avatarElement = textAvatar;
                        rect = avatarElement.getBoundingClientRect();
                        console.log('📍 Using text avatar for positioning');
                    } else {
                        // 回退到用户profile容器
                        avatarElement = this.userProfile;
                        rect = avatarElement.getBoundingClientRect();
                        console.log('📍 Using user profile container for positioning');
                    }
                }

                const dropdownWidth = 280; // 下拉菜单宽度
                const margin = 20; // 边距

                console.log('📍 Avatar element rect:', rect);

                // 计算下拉菜单位置 - 居中对齐到头像正下方
                let top = rect.bottom + 8; // 头像下方8px
                let left = rect.left + (rect.width / 2) - (dropdownWidth / 2); // 居中对齐

                console.log('📍 Initial position - top:', top, 'left:', left);

                // 确保不超出屏幕左边界
                if (left < margin) {
                    left = margin;
                    console.log('📍 Adjusted left for left boundary:', left);
                }

                // 确保不超出屏幕右边界
                if (left + dropdownWidth > window.innerWidth - margin) {
                    left = window.innerWidth - dropdownWidth - margin;
                    console.log('📍 Adjusted left for right boundary:', left);
                }

                // 确保不超出屏幕下边界
                const estimatedDropdownHeight = 400; // 假设下拉菜单高度约400px
                if (top + estimatedDropdownHeight > window.innerHeight) {
                    top = rect.top - 8 - estimatedDropdownHeight; // 显示在头像上方
                    console.log('📍 Adjusted top for bottom boundary:', top);
                }

                console.log('📍 Final position - top:', top, 'left:', left);

                // 应用位置 - 使用left定位而不是right
                this.profileDropdown.style.top = `${top}px`;
                this.profileDropdown.style.left = `${left}px`;
                this.profileDropdown.style.right = 'auto';
                this.profileDropdown.style.bottom = 'auto';

                console.log('📍 Dropdown positioned below avatar center');
            }

            checkAuthStatus() {
                const token = localStorage.getItem('access_token');
                console.log('🔍 Checking auth status, token:', token ? 'exists' : 'not found');

                if (token && !this.isTokenExpired(token)) {
                    console.log('✅ Token valid, showing user profile');
                    this.showUserProfile();
                    this.fetchUserData();
                } else {
                    console.log('❌ No valid token, showing auth buttons');
                    this.showAuthButtons();
                }
            }

            isTokenExpired(token) {
                try {
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    return payload.exp * 1000 < Date.now();
                } catch (e) {
                    return true;
                }
            }

            showUserProfile() {
                this.authButtons.style.display = 'none';
                this.userProfile.style.display = 'inline-flex';

                // 通知系统用户已登录
                if (window.notificationSystem) {
                    window.notificationSystem.onUserLogin();
                }
            }

            showAuthButtons() {
                this.authButtons.style.display = 'flex';
                this.userProfile.style.display = 'none';
            }

            async fetchUserData() {
                try {
                    const token = localStorage.getItem('access_token');
                    const apiBaseUrl = '{{ API_BASE_URL }}';
                    console.log('Fetching user data from:', `${apiBaseUrl}/api/user/profile/`);

                    const response = await fetch(`${apiBaseUrl}/api/user/profile/`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    console.log('Profile API response status:', response.status);

                    if (response.ok) {
                        const userData = await response.json();
                        console.log('User data received:', userData);
                        this.updateUserProfile(userData);
                    } else {
                        // 如果API调用失败，使用默认用户信息
                        console.log('Profile API failed, using default user info');
                        this.updateUserProfile({
                            first_name: 'User',
                            last_name: '',
                            email: '<EMAIL>',
                            avatar: null
                        });
                    }
                } catch (error) {
                    console.error('Error fetching user data:', error);
                    // 即使API失败，也显示用户profile（使用默认信息）
                    this.updateUserProfile({
                        first_name: 'User',
                        last_name: '',
                        email: '<EMAIL>',
                        avatar: null
                    });
                }
            }

            updateUserProfile(userData) {
                console.log('Updating user profile with data:', userData);

                // 处理API响应格式（可能包含data字段）
                const user = userData.data || userData.user || userData;

                // 更新用户信息 - 优先显示完整姓名
                let displayName = '';
                if (user.first_name && user.last_name) {
                    displayName = `${user.first_name} ${user.last_name}`.trim();
                } else if (user.first_name) {
                    displayName = user.first_name;
                } else if (user.last_name) {
                    displayName = user.last_name;
                } else if (user.display_name) {
                    displayName = user.display_name;
                } else if (user.full_name) {
                    displayName = user.full_name;
                } else if (user.email) {
                    // 从邮箱提取用户名作为备用
                    displayName = user.email.split('@')[0];
                } else {
                    displayName = 'User';
                }

                this.profileName.textContent = displayName;
                this.profileEmail.textContent = user.email || '';

                // 更新头像
                this.updateAvatar(user, displayName);

                // Check if user is admin and show admin menu items
                this.checkAndShowAdminMenu(user);

                console.log('Profile updated - Name:', this.profileName.textContent, 'Email:', this.profileEmail.textContent);
            }

            checkAndShowAdminMenu(user) {
                // Check if user has admin privileges
                const isAdmin = user.is_staff ||
                               user.is_superuser ||
                               (user.groups && user.groups.some(group => group.name === 'Data Analysts'));

                const adminMenuItems = document.getElementById('admin-menu-items');
                if (adminMenuItems) {
                    if (isAdmin) {
                        adminMenuItems.style.display = 'block';
                        console.log('✅ Admin menu items shown for admin user');
                    } else {
                        adminMenuItems.style.display = 'none';
                        console.log('ℹ️ Admin menu items hidden for regular user');
                    }
                }
            }

            updateAvatar(user, displayName) {
                console.log('🖼️ Updating avatar for user:', user);
                console.log('🖼️ User avatar field:', user.avatar);

                // 检查是否有上传的头像
                if (user.avatar && user.avatar !== null && user.avatar !== '') {
                    let avatarUrl = user.avatar;

                    // 如果头像URL不是完整URL，添加API基础URL
                    if (!avatarUrl.startsWith('http') && !avatarUrl.includes('static')) {
                        avatarUrl = `${window.API_BASE_URL}${avatarUrl}`;
                    }

                    console.log('🖼️ Using image avatar:', avatarUrl);

                    // 显示图片头像
                    this.profileAvatar.src = avatarUrl;
                    this.profileAvatarLarge.src = avatarUrl;
                    this.profileAvatar.style.display = 'block';
                    this.profileAvatarLarge.style.display = 'block';

                    // 隐藏文字头像
                    const textAvatars = document.querySelectorAll('.profile-avatar-text');
                    textAvatars.forEach(avatar => avatar.style.display = 'none');
                } else {
                    console.log('🖼️ No image avatar, generating text avatar');

                    // 生成文字头像
                    const initials = this.generateInitials(user.first_name, user.last_name, user.email);
                    console.log('🖼️ Generated initials:', initials);
                    this.createTextAvatar(initials);
                }
            }

            generateInitials(firstName, lastName, email) {
                // 优先使用姓名首字母
                if (firstName && lastName && lastName.trim() !== '') {
                    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
                } else if (firstName && firstName.trim() !== '') {
                    return firstName.charAt(0).toUpperCase();
                } else if (lastName && lastName.trim() !== '') {
                    return lastName.charAt(0).toUpperCase();
                } else if (email) {
                    return email.charAt(0).toUpperCase();
                } else {
                    return 'U';
                }
            }

            createTextAvatar(initials) {
                console.log('🔤 Creating text avatar with initials:', initials);

                // 隐藏图片头像
                this.profileAvatar.style.display = 'none';
                this.profileAvatarLarge.style.display = 'none';

                console.log('🔤 Profile avatar parent:', this.profileAvatar.parentElement);
                console.log('🔤 Profile avatar large parent:', this.profileAvatarLarge.parentElement);

                // 创建或更新文字头像
                console.log('🔤 Creating small text avatar for header');
                this.createTextAvatarElement(this.profileAvatar.parentElement, initials, 'profile-avatar-text-small');

                console.log('🔤 Creating large text avatar for dropdown');
                this.createTextAvatarElement(this.profileAvatarLarge.parentElement, initials, 'profile-avatar-text-large');

                console.log('🔤 Text avatars created successfully');
            }

            createTextAvatarElement(container, initials, className) {
                console.log('🎨 Creating text avatar element:', { container, initials, className });

                let textAvatar = container.querySelector(`.${className}`);

                if (!textAvatar) {
                    console.log('🎨 Creating new text avatar element');
                    textAvatar = document.createElement('div');
                    textAvatar.className = `profile-avatar-text ${className}`;

                    // 对于大头像（下拉菜单），确保插入到正确位置（第一个位置）
                    if (className.includes('large')) {
                        console.log('🎨 Inserting large text avatar at the beginning');
                        container.insertBefore(textAvatar, container.firstChild);
                    } else {
                        console.log('🎨 Appending small text avatar');
                        container.appendChild(textAvatar);
                    }
                } else {
                    console.log('🎨 Using existing text avatar element');
                }

                textAvatar.textContent = initials;
                textAvatar.style.display = 'flex';

                console.log('🎨 Text avatar element created:', textAvatar);

                // 设置样式
                const isLarge = className.includes('large');
                textAvatar.style.cssText = `
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: ${isLarge ? '48px' : '36px'};
                    height: ${isLarge ? '48px' : '36px'};
                    border-radius: 50%;
                    background: linear-gradient(135deg, #0066cc 0%, #2c5aa0 100%);
                    color: white;
                    font-weight: 600;
                    font-size: ${isLarge ? '20px' : '14px'};
                    font-family: 'Inter', sans-serif;
                    cursor: pointer;
                    user-select: none;
                    border: ${isLarge ? '3px solid rgba(255, 255, 255, 0.3)' : '2px solid transparent'};
                    transition: all 0.2s ease;
                `;

                // 添加悬停效果
                textAvatar.addEventListener('mouseenter', () => {
                    textAvatar.style.transform = 'scale(1.05)';
                    if (!isLarge) {
                        textAvatar.style.borderColor = '#3b82f6';
                    }
                });

                textAvatar.addEventListener('mouseleave', () => {
                    textAvatar.style.transform = 'scale(1)';
                    if (!isLarge) {
                        textAvatar.style.borderColor = 'transparent';
                    }
                });
            }

            async logout() {
                try {
                    const token = localStorage.getItem('access_token');
                    const refreshToken = localStorage.getItem('refresh_token');
                    const apiBaseUrl = '{{ API_BASE_URL }}';

                    // 发送登出请求
                    await fetch(`${apiBaseUrl}/api/logout/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify({
                            refresh_token: refreshToken
                        })
                    });
                } catch (error) {
                    console.error('Logout error:', error);
                } finally {
                    // 清除本地存储
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('refresh_token');

                    // 关闭下拉菜单
                    this.closeDropdown();

                    // 显示登录按钮
                    this.showAuthButtons();

                    // 通知系统用户已登出
                    if (window.notificationSystem) {
                        window.notificationSystem.onUserLogout();
                    }

                    // 重定向到首页
                    window.location.href = '{% url "home" %}';
                }
            }
        }

        // 移动端菜单切换
        function toggleHeader() {
            const headerItems = document.getElementById('collapsed-header-items');
            const collapseBtn = document.getElementById('collapse-btn');

            headerItems.classList.toggle('tw-hidden');
            collapseBtn.classList.toggle('bi-list');
            collapseBtn.classList.toggle('bi-x');
        }

        // 简化的通知系统 - 仅用于徽章更新
        class NotificationSystem {
            constructor() {
                this.unreadCount = 0;
                this.pollInterval = null;
                this.init();
            }

            init() {
                // 如果用户已登录，开始轮询通知
                if (this.isUserLoggedIn()) {
                    this.startPolling();
                }
            }

            isUserLoggedIn() {
                const token = localStorage.getItem('access_token');
                return token && token !== 'null' && token !== '';
            }

            async loadNotifications() {
                if (!this.isUserLoggedIn()) return;

                try {
                    const token = localStorage.getItem('access_token');
                    const apiBaseUrl = '{{ API_BASE_URL }}';

                    const response = await fetch(`${apiBaseUrl}/api/user/notifications/`, {
                        headers: {
                            'Authorization': `Bearer ${token}`,
                            'Content-Type': 'application/json'
                        }
                    });

                    if (response.ok) {
                        const data = await response.json();
                        if (data.success) {
                            this.unreadCount = data.data.unread_count || 0;
                            this.updateBadge();
                        }
                    }
                } catch (error) {
                    console.error('Failed to load notifications:', error);
                }
            }

            updateBadge() {
                const badge = document.getElementById('notification-badge');
                if (badge) {
                    if (this.unreadCount > 0) {
                        badge.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
                        badge.style.display = 'flex';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            }



            startPolling() {
                // 立即加载一次
                this.loadNotifications();

                // 每30秒轮询一次
                this.pollInterval = setInterval(() => {
                    this.loadNotifications();
                }, 30000);
            }

            stopPolling() {
                if (this.pollInterval) {
                    clearInterval(this.pollInterval);
                    this.pollInterval = null;
                }
            }

            onUserLogin() {
                this.startPolling();
            }

            onUserLogout() {
                this.stopPolling();
                this.unreadCount = 0;
                this.updateBadge();
            }
        }

        // 跳转到通知页面
        function goToNotifications(event) {
            // 阻止事件冒泡，防止触发其他点击事件
            if (event) {
                event.stopPropagation();
                event.preventDefault();
            }

            console.log('🔔 Notification bell clicked - checking authentication');

            // Check authentication before redirecting
            const token = localStorage.getItem('access_token');
            if (!token) {
                console.log('🔔 No token found, redirecting to login');
                window.location.href = '/login/';
                return;
            }

            console.log('🔔 User authenticated, redirecting to notifications page');
            // 跳转到通知页面
            window.location.href = '/notifications/';
        }











        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing UserProfile');
            const userProfile = new UserProfile();
            window.userProfile = userProfile; // 全局访问

            // 初始化通知系统
            window.notificationSystem = new NotificationSystem();

            // 通知面板现在相对于铃铛定位，无需监听窗口大小变化

            // 调试信息
            console.log('Auth buttons element:', document.getElementById('auth-buttons'));
            console.log('User profile element:', document.getElementById('user-profile'));
            console.log('Current token:', localStorage.getItem('access_token') ? 'exists' : 'not found');

            // Add global function to refresh authentication status
            window.refreshAuthStatus = function() {
                console.log('🔄 Refreshing authentication status globally');
                userProfile.checkAuthStatus();
            };

            // Listen for page visibility changes to refresh auth status
            document.addEventListener('visibilitychange', function() {
                if (!document.hidden) {
                    console.log('🔍 Page became visible, refreshing auth status');
                    setTimeout(() => {
                        userProfile.checkAuthStatus();
                    }, 100);
                }
            });

            // Listen for focus events to refresh auth status
            window.addEventListener('focus', function() {
                console.log('🔍 Window gained focus, refreshing auth status');
                setTimeout(() => {
                    userProfile.checkAuthStatus();
                }, 100);
            });

            // Handle global sign in button
            const globalSigninBtn = document.getElementById('global-signin-btn');
            if (globalSigninBtn) {
                globalSigninBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    console.log('🔗 Global sign in button clicked');

                    // Store current page as both original and redirect target
                    const currentUrl = window.location.href;

                    // Clear any existing stored URLs to prevent conflicts
                    sessionStorage.removeItem('redirectAfterLogin');
                    sessionStorage.removeItem('originalPageBeforeLogin');

                    // For sign in button, user should return to the same page
                    sessionStorage.setItem('redirectAfterLogin', currentUrl);
                    sessionStorage.setItem('originalPageBeforeLogin', currentUrl);

                    console.log('📝 Stored current page for return after login:', currentUrl);

                    // Navigate to login page
                    window.location.href = '/login/';
                });
            }

            // 添加测试函数到全局作用域
            window.testDefaultAvatar = function() {
                console.log('🧪 Testing default avatar generation');
                userProfile.showUserProfile(); // 确保用户profile显示
                userProfile.updateUserProfile({
                    first_name: 'User',
                    last_name: '',
                    email: '<EMAIL>',
                    avatar: null
                });
            };

            // 添加测试下拉菜单功能
            window.testDropdown = function() {
                console.log('🧪 Testing dropdown functionality');
                userProfile.showUserProfile(); // 确保用户profile显示

                // 先设置一个默认用户来测试文字头像
                userProfile.updateUserProfile({
                    first_name: 'User',
                    last_name: '',
                    email: '<EMAIL>',
                    avatar: null
                });

                setTimeout(() => {
                    console.log('🧪 Attempting to toggle dropdown');
                    userProfile.toggleDropdown();

                    // 检查下拉菜单的位置和头像显示
                    setTimeout(() => {
                        const userProfileElement = document.getElementById('user-profile');
                        const dropdown = document.getElementById('profile-dropdown');
                        const profileInfo = document.querySelector('.profile-info');
                        const largeTextAvatar = document.querySelector('.profile-avatar-text-large');
                        const profileDetails = document.querySelector('.profile-details');

                        // 检查下拉菜单位置
                        if (userProfileElement && dropdown) {
                            const userRect = userProfileElement.getBoundingClientRect();
                            const dropdownRect = dropdown.getBoundingClientRect();

                            console.log('🧪 User profile rect:', userRect);
                            console.log('🧪 Dropdown rect:', dropdownRect);
                            console.log('🧪 Dropdown is below avatar:', dropdownRect.top > userRect.bottom);
                            console.log('🧪 Dropdown is centered:', Math.abs((userRect.left + userRect.width/2) - (dropdownRect.left + dropdownRect.width/2)) < 10);
                        }

                        // 检查头像位置
                        if (profileInfo && largeTextAvatar && profileDetails) {
                            const avatarIndex = Array.from(profileInfo.children).indexOf(largeTextAvatar);
                            const detailsIndex = Array.from(profileInfo.children).indexOf(profileDetails);
                            console.log('🧪 Avatar is before details:', avatarIndex < detailsIndex);
                        }
                    }, 200);
                }, 500);
            };

            // 添加测试有头像用户的功能
            window.testUserWithAvatar = function() {
                console.log('🧪 Testing user with avatar');
                userProfile.showUserProfile();

                // 设置一个有头像的用户
                userProfile.updateUserProfile({
                    first_name: 'John',
                    last_name: 'Doe',
                    email: '<EMAIL>',
                    avatar: '{% static "assets/images/default-avatar.svg" %}'
                });

                setTimeout(() => {
                    console.log('🧪 Opening dropdown for user with avatar');
                    userProfile.toggleDropdown();

                    // 检查图片头像的定位
                    setTimeout(() => {
                        const imageAvatar = document.getElementById('profile-avatar');
                        const dropdown = document.getElementById('profile-dropdown');

                        if (imageAvatar && dropdown) {
                            const avatarRect = imageAvatar.getBoundingClientRect();
                            const dropdownRect = dropdown.getBoundingClientRect();

                            console.log('🧪 Image avatar rect:', avatarRect);
                            console.log('🧪 Dropdown rect:', dropdownRect);

                            const avatarCenterX = avatarRect.left + avatarRect.width / 2;
                            const dropdownCenterX = dropdownRect.left + dropdownRect.width / 2;
                            const centerDiff = Math.abs(avatarCenterX - dropdownCenterX);

                            console.log('🧪 Avatar center X:', avatarCenterX);
                            console.log('🧪 Dropdown center X:', dropdownCenterX);
                            console.log('🧪 Center difference:', centerDiff);
                            console.log('🧪 Is centered (within 5px):', centerDiff < 5);
                        }
                    }, 200);
                }, 500);
            };

            // 添加专门测试文字头像定位的功能
            window.testTextAvatarPositioning = function() {
                console.log('🧪 Testing text avatar positioning');
                userProfile.showUserProfile();

                // 设置默认用户（文字头像）
                userProfile.updateUserProfile({
                    first_name: 'User',
                    last_name: '',
                    email: '<EMAIL>',
                    avatar: null
                });

                setTimeout(() => {
                    console.log('🧪 Opening dropdown for text avatar');
                    userProfile.toggleDropdown();

                    // 检查文字头像的定位
                    setTimeout(() => {
                        const textAvatar = document.querySelector('.profile-avatar-text-small');
                        const dropdown = document.getElementById('profile-dropdown');

                        if (textAvatar && dropdown) {
                            const avatarRect = textAvatar.getBoundingClientRect();
                            const dropdownRect = dropdown.getBoundingClientRect();

                            console.log('🧪 Text avatar rect:', avatarRect);
                            console.log('🧪 Dropdown rect:', dropdownRect);

                            const avatarCenterX = avatarRect.left + avatarRect.width / 2;
                            const dropdownCenterX = dropdownRect.left + dropdownRect.width / 2;
                            const centerDiff = Math.abs(avatarCenterX - dropdownCenterX);

                            console.log('🧪 Text avatar center X:', avatarCenterX);
                            console.log('🧪 Dropdown center X:', dropdownCenterX);
                            console.log('🧪 Center difference:', centerDiff);
                            console.log('🧪 Is centered (within 5px):', centerDiff < 5);
                            console.log('🧪 Is below avatar:', dropdownRect.top > avatarRect.bottom);
                        }
                    }, 200);
                }, 500);
            };
        });
    </script>

    <!-- Navigation Manager - Global -->
    <script src="{% static 'js/navigation-manager.js' %}"></script>

    <!-- Global post-login setup -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Check if this page was reached after login and setup history
            if (window.checkPostLoginSetup) {
                window.checkPostLoginSetup();
            }
        });
    </script>

    {% block extra_js %}{% endblock extra_js %}
</body>
</html>
